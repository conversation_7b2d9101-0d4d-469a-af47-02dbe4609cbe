import { system } from "@minecraft/server";
export function mxCheckForWallHit(mx) {
    try {
        const isDashing = mx.getProperty("ditsh:dashing");
        if (!isDashing)
            return;
        const location = { x: mx.location.x, y: mx.location.y + 2, z: mx.location.z };
        const direction = mx.getViewDirection();
        const offset = { x: location.x + direction.x * 3, y: location.y, z: location.z + direction.z * 3 };
        const block = mx.dimension.getBlock(offset);
        if (block && !block.isAir) {
            mx.triggerEvent("ditsh:on_dash_attack");
            mx.dimension.playSound("mob.ditsh.mx.hit_wall", location);
        }
    }
    catch (e) { }
    return;
}
export async function mxJump(mx) {
    try {
        mx.setProperty("ditsh:jumping", true);
        await system.waitTicks(10);
    }
    catch (e) { }
    return;
}
